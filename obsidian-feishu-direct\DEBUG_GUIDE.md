# 飞书分享权限设置调试指南

## 问题现象
在分享文档时出现 400 错误：
```
Setting document share permissions for EgBgd2FN8ooUwSxuKCMclkOVnsc: Object
Set document share permissions error: Error: Request failed, status 400
```

## 已实施的修复措施

### 1. 简化权限设置参数
- 移除了复杂的权限组合设置
- 只设置核心的 `link_share_entity` 参数
- 减少API调用失败的可能性

### 2. 增加详细的错误日志
- 添加了响应状态码记录
- 增加了请求参数和响应数据的详细日志
- 改进了错误信息的可读性

### 3. 添加文档创建延迟
- 在设置权限前等待3秒，确保文档完全创建
- 避免在文档未完全就绪时设置权限

### 4. 获取当前权限状态
- 在设置权限前先获取当前权限状态
- 用于调试和对比权限变化

## 测试步骤

### 步骤1：重新加载插件
1. 在Obsidian中按 `Ctrl+Shift+I` 打开开发者工具
2. 重新加载插件或重启Obsidian
3. 确保新的代码已生效

### 步骤2：检查设置
1. 打开插件设置页面
2. 确认"启用链接分享"开关已打开
3. 选择合适的权限级别（建议先用 `tenant_readable`）

### 步骤3：测试分享
1. 创建一个简单的测试文档
2. 右键选择"分享到飞书"
3. 观察控制台输出

### 步骤4：分析日志
在控制台中查找以下关键信息：
- `📋 Current document permissions:` - 当前权限状态
- `🔧 Setting document share permissions for` - 设置权限的请求参数
- `📋 Set document permissions response status:` - API响应状态
- `📋 Set document permissions response data:` - API响应数据

## 可能的问题和解决方案

### 问题1：权限不足
**现象**：API返回权限相关错误
**解决方案**：
1. 检查飞书应用是否有足够的权限
2. 确认需要以下权限：
   - `drive:drive` - 访问云空间文件
   - `docx:document` - 创建、编辑文档
   - 可能需要额外的权限管理权限

### 问题2：文档状态问题
**现象**：新创建的文档无法立即设置权限
**解决方案**：
1. 已添加3秒延迟，如果仍有问题可以增加延迟时间
2. 检查文档是否完全创建成功

### 问题3：API参数格式问题
**现象**：400错误，参数不正确
**解决方案**：
1. 检查控制台中的请求参数格式
2. 对比飞书API文档确认参数正确性

### 问题4：Token权限问题
**现象**：Token相关错误
**解决方案**：
1. 重新授权获取新的Token
2. 确认Token有足够的权限范围

## 临时解决方案

如果权限设置持续失败，可以临时禁用该功能：

### 方法1：通过设置禁用
1. 打开插件设置
2. 关闭"启用链接分享"开关
3. 分享功能仍然正常，只是不会自动设置链接权限

### 方法2：代码级禁用
在 `feishu-api.ts` 中临时注释掉权限设置代码：
```typescript
// 临时禁用权限设置
// if (this.settings.enableLinkShare) {
//     await this.setDocumentSharePermissions(finalResult.documentToken);
// }
```

## 权限设置验证

### 最新优化 (v2.4) - 用户体验优化
基于用户反馈进行的优化：
- ✅ 修正API参数值：`anyone_readable` / `anyone_editable` (正确的互联网访问参数)
- ✅ 移除不必要的3秒延迟，提升分享速度
- ✅ 简化设置界面：移除"关闭链接分享"选项，通过开关控制
- ✅ 清理冗余代码，提升代码质量
- ✅ 默认设置为互联网可访问，符合用户期望

### 验证步骤
1. **重新加载插件**：确保最新代码生效
2. **分享测试文档**：创建并分享一个新文档
3. **检查权限设置**：在控制台查看设置的权限参数：
   ```
   🔧 Setting document share permissions for xxx: {
     link_share_entity: 'tenant_readable',
     external_access_entity: 'open',        // 新增：关键参数
     share_entity: 'anyone',
     manage_collaborator_entity: 'collaborator_can_view'
   }
   ```

### 测试链接分享效果
1. **获取分享链接**：复制插件返回的文档链接
2. **切换账号测试**：
   - 用同组织的其他飞书账号打开链接
   - 或者在无痕浏览器中打开（需要登录同组织账号）
3. **验证访问权限**：确认其他人可以阅读文档内容

### 预期的权限设置结果

#### 互联网访问模式（默认）
```json
{
  "link_share_entity": "anyone_can_read",     // 互联网上获得链接的任何人可阅读
  "external_access_entity": "open",           // 允许外部访问（必需）
  "share_entity": "anyone",                   // 任何有权限的人可查看协作者
  "manage_collaborator_entity": "collaborator_can_view"  // 协作者可查看其他协作者
}
```

#### 组织内访问模式
```json
{
  "link_share_entity": "tenant_readable",     // 组织内获得链接的人可阅读
  "external_access_entity": "open",           // 允许外部访问
  "share_entity": "anyone",                   // 任何有权限的人可查看协作者
  "manage_collaborator_entity": "collaborator_can_view"  // 协作者可查看其他协作者
}
```

## 下一步调试

1. **收集详细日志**：运行测试并收集完整的控制台日志
2. **API文档对比**：将请求参数与飞书官方API文档对比
3. **权限验证**：确认应用权限配置是否正确
4. **手动测试**：在飞书网页端手动设置文档权限，观察效果
5. **链接分享测试**：用其他账号测试链接访问效果

## 联系支持

如果问题持续存在，请提供：
1. 完整的控制台错误日志
2. 飞书应用的权限配置截图
3. 插件设置页面截图
4. 具体的错误复现步骤
5. 链接分享测试结果（其他人是否能访问）
