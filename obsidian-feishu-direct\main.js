/*
THIS IS A GENERATED/BUNDLED FILE BY ESBUILD
if you want to view the source, please visit the github repository of this plugin
*/

var D=Object.defineProperty;var N=Object.getOwnPropertyDescriptor;var W=Object.getOwnPropertyNames;var z=Object.prototype.hasOwnProperty;var H=(S,s)=>{for(var e in s)D(S,e,{get:s[e],enumerable:!0})},V=(S,s,e,t)=>{if(s&&typeof s=="object"||typeof s=="function")for(let r of W(s))!z.call(S,r)&&r!==e&&D(S,r,{get:()=>s[r],enumerable:!(t=N(s,r))||t.enumerable});return S};var J=S=>V(D({},"__esModule",{value:!0}),S);var q={};H(q,{default:()=>R});module.exports=J(q);var f=require("obsidian");var p={BASE_URL:"https://open.feishu.cn/open-apis",AUTHORIZE_URL:"https://open.feishu.cn/open-apis/authen/v1/authorize",TOKEN_URL:"https://open.feishu.cn/open-apis/authen/v1/access_token",REFRESH_TOKEN_URL:"https://open.feishu.cn/open-apis/authen/v1/refresh_access_token",SCOPES:"contact:user.base:readonly docx:document drive:drive",UPLOAD_URL:"https://open.feishu.cn/open-apis/drive/v1/files/upload_all",DOC_CREATE_URL:"https://open.feishu.cn/open-apis/docx/v1/documents",FOLDER_LIST_URL:"https://open.feishu.cn/open-apis/drive/v1/files",USER_INFO_URL:"https://open.feishu.cn/open-apis/authen/v1/user_info"},O={appId:"",appSecret:"",callbackUrl:"https://md2feishu.xinqi.life/oauth-callback",accessToken:"",refreshToken:"",userInfo:null,defaultFolderId:"",defaultFolderName:"\u6211\u7684\u7A7A\u95F4",titleSource:"filename",frontMatterHandling:"remove",enableLinkShare:!0,linkSharePermission:"anyone_readable"},B={1061002:"\u53C2\u6570\u9519\u8BEF\uFF0C\u8BF7\u68C0\u67E5\u6587\u4EF6\u683C\u5F0F\u548C\u5927\u5C0F",1061005:"\u6587\u4EF6\u5927\u5C0F\u8D85\u51FA\u9650\u5236",1061006:"\u6587\u4EF6\u7C7B\u578B\u4E0D\u652F\u6301",99991663:"access_token \u65E0\u6548",99991664:"access_token \u5DF2\u8FC7\u671F",99991665:"refresh_token \u65E0\u6548",99991666:"refresh_token \u5DF2\u8FC7\u671F"},M={note:{emoji:"\u{1F4DD}",color:"blue",title:"\u7B14\u8BB0"},info:{emoji:"\u2139\uFE0F",color:"blue",title:"\u4FE1\u606F"},tip:{emoji:"\u{1F4A1}",color:"green",title:"\u63D0\u793A"},hint:{emoji:"\u{1F4A1}",color:"green",title:"\u63D0\u793A"},warning:{emoji:"\u26A0\uFE0F",color:"yellow",title:"\u8B66\u544A"},caution:{emoji:"\u26A0\uFE0F",color:"yellow",title:"\u6CE8\u610F"},attention:{emoji:"\u26A0\uFE0F",color:"yellow",title:"\u6CE8\u610F"},error:{emoji:"\u274C",color:"red",title:"\u9519\u8BEF"},danger:{emoji:"\u26D4",color:"red",title:"\u5371\u9669"},failure:{emoji:"\u274C",color:"red",title:"\u5931\u8D25"},fail:{emoji:"\u274C",color:"red",title:"\u5931\u8D25"},missing:{emoji:"\u2753",color:"red",title:"\u7F3A\u5931"},success:{emoji:"\u2705",color:"green",title:"\u6210\u529F"},check:{emoji:"\u2705",color:"green",title:"\u68C0\u67E5"},done:{emoji:"\u2705",color:"green",title:"\u5B8C\u6210"},question:{emoji:"\u2753",color:"purple",title:"\u95EE\u9898"},help:{emoji:"\u2753",color:"purple",title:"\u5E2E\u52A9"},faq:{emoji:"\u2753",color:"purple",title:"\u5E38\u89C1\u95EE\u9898"},quote:{emoji:"\u{1F4AC}",color:"gray",title:"\u5F15\u7528"},cite:{emoji:"\u{1F4D6}",color:"gray",title:"\u5F15\u7528"},abstract:{emoji:"\u{1F4C4}",color:"cyan",title:"\u6458\u8981"},summary:{emoji:"\u{1F4C4}",color:"cyan",title:"\u603B\u7ED3"},tldr:{emoji:"\u{1F4C4}",color:"cyan",title:"TL;DR"},example:{emoji:"\u{1F4CB}",color:"purple",title:"\u793A\u4F8B"},todo:{emoji:"\u2611\uFE0F",color:"blue",title:"\u5F85\u529E"},default:{emoji:"\u{1F4CC}",color:"blue",title:"\u63D0\u793A"}};var u=require("obsidian");var P=class{constructor(s,e){this.settings=s,this.app=e}updateSettings(s){this.settings=s}generateAuthUrl(){if(!this.settings.appId||!this.settings.appSecret)throw new Error("\u8BF7\u5148\u5728\u8BBE\u7F6E\u4E2D\u914D\u7F6E\u98DE\u4E66\u5E94\u7528\u7684 App ID \u548C App Secret");let s=this.generateRandomState();localStorage.setItem("feishu-oauth-state",s);let e=this.settings.callbackUrl,t=new URLSearchParams({app_id:this.settings.appId,redirect_uri:e,scope:p.SCOPES,state:s,response_type:"code"});return`${p.AUTHORIZE_URL}?${t.toString()}`}async processCallback(s){try{let e=new URL(s),t=e.searchParams.get("code"),r=e.searchParams.get("state"),o=e.searchParams.get("error");if(o)return console.error("OAuth error:",o),!1;if(!t)return console.error("No authorization code in callback"),!1;let n=localStorage.getItem("feishu-oauth-state");return n&&r!==n?(console.error("State mismatch"),!1):await this.handleOAuthCallback(t)}catch(e){return console.error("Process callback error:",e),!1}}async handleOAuthCallback(s){try{if(!this.settings.appId||!this.settings.appSecret)throw new Error("\u5E94\u7528\u914D\u7F6E\u4E0D\u5B8C\u6574");let e=await this.exchangeCodeForToken(s);if(!e.success)throw new Error(e.error||"\u83B7\u53D6\u8BBF\u95EE\u4EE4\u724C\u5931\u8D25");let t=await this.getUserInfo();if(t)return this.settings.userInfo=t,new u.Notice("\u2705 \u98DE\u4E66\u6388\u6743\u6210\u529F\uFF01"),!0;throw new Error("\u83B7\u53D6\u7528\u6237\u4FE1\u606F\u5931\u8D25")}catch(e){return console.error("OAuth callback error:",e),new u.Notice(`\u274C \u6388\u6743\u5931\u8D25: ${e.message}`),!1}}async exchangeCodeForToken(s){try{let e=await(0,u.requestUrl)({url:"https://open.feishu.cn/open-apis/auth/v3/app_access_token/internal",method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({app_id:this.settings.appId,app_secret:this.settings.appSecret})}),t=e.json||JSON.parse(e.text);if(t.code!==0)return console.error("Failed to get app access token:",t),{success:!1,error:`\u83B7\u53D6\u5E94\u7528\u4EE4\u724C\u5931\u8D25: ${t.msg}`};let r=t.app_access_token,o={grant_type:"authorization_code",code:s},n=await(0,u.requestUrl)({url:p.TOKEN_URL,method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${r}`},body:JSON.stringify(o)}),i;if(n.json&&typeof n.json=="object")i=n.json;else if(n.text){let a=n.text;i=JSON.parse(a)}else console.log("Trying to call response.json()..."),i=await n.json();return i.code===0?(this.settings.accessToken=i.data.access_token,this.settings.refreshToken=i.data.refresh_token,{success:!0}):(console.error("Token exchange failed:",i),{success:!1,error:i.msg})}catch(e){return console.error("Token exchange error:",e),{success:!1,error:e.message}}}async getUserInfo(){try{let s=await(0,u.requestUrl)({url:p.USER_INFO_URL,method:"GET",headers:{Authorization:`Bearer ${this.settings.accessToken}`,"Content-Type":"application/json"}}),e=s.json||JSON.parse(s.text);return e.code===0?{name:e.data.name,avatar_url:e.data.avatar_url,email:e.data.email,user_id:e.data.user_id}:(console.error("Get user info failed:",e),null)}catch(s){return console.error("Get user info error:",s),null}}async shareMarkdownWithFiles(s,e,t){try{if(t&&t.setMessage("\u{1F50D} \u6B63\u5728\u68C0\u67E5\u6388\u6743\u72B6\u6001..."),!await this.ensureValidTokenWithReauth(t))throw new Error("\u6388\u6743\u5931\u6548\u4E14\u91CD\u65B0\u6388\u6743\u5931\u8D25\uFF0C\u8BF7\u624B\u52A8\u91CD\u65B0\u6388\u6743");t&&t.setMessage("\u{1F4E4} \u6B63\u5728\u4E0A\u4F20\u6587\u4EF6\u5230\u98DE\u4E66...");let o=await this.uploadMarkdownFile(s,e.content);if(!o.success)throw new Error(o.error||"\u6587\u4EF6\u4E0A\u4F20\u5931\u8D25");if(!o.fileToken)throw new Error("\u6587\u4EF6\u4E0A\u4F20\u6210\u529F\u4F46\u672A\u83B7\u53D6\u5230\u6587\u4EF6\u4EE4\u724C");let n=o.url||`https://feishu.cn/file/${o.fileToken}`;try{let i=s.endsWith(".md")?s.slice(0,-3):s,a=await this.createImportTaskWithCorrectFolder(o.fileToken,i);if(a.success&&a.ticket){console.log("Step 3: Waiting for import completion (15s timeout)...");let l=await this.waitForImportCompletionWithTimeout(a.ticket,15e3);if(l.success&&l.documentToken){let h=`https://feishu.cn/docx/${l.documentToken}`;if(this.settings.enableLinkShare)try{t&&t.setMessage("\u{1F517} \u6B63\u5728\u8BBE\u7F6E\u6587\u6863\u5206\u4EAB\u6743\u9650..."),await this.setDocumentSharePermissions(l.documentToken),console.log("\u2705 Document share permissions set successfully")}catch(c){console.warn("\u26A0\uFE0F Failed to set document share permissions:",c)}if(e.localFiles.length>0)try{let c=e.localFiles.filter(b=>b.isSubDocument),k=e.localFiles.filter(b=>!b.isSubDocument);c.length>0&&(t&&t.setMessage(`\u{1F4C4} \u6B63\u5728\u5904\u7406 ${c.length} \u4E2A\u5B50\u6587\u6863...`),await this.processSubDocuments(l.documentToken,c,t)),k.length>0&&(t&&t.setMessage(`\u{1F4CE} \u6B63\u5728\u5904\u7406 ${k.length} \u4E2A\u9644\u4EF6...`),await this.processFileUploads(l.documentToken,k,t))}catch(c){console.warn("\u26A0\uFE0F File upload processing failed:",c)}try{await this.deleteSourceFile(o.fileToken)}catch(c){console.warn("\u26A0\uFE0F Failed to delete source file:",c.message)}return{success:!0,title:i,url:h}}else return console.warn("\u26A0\uFE0F Import task failed or timed out, falling back to file URL"),console.warn("Final result details:",l),{success:!0,title:s,url:n}}else return console.warn("\u26A0\uFE0F Failed to create import task, falling back to file URL"),console.warn("Import result details:",a),{success:!0,title:s,url:n}}catch(i){return console.warn("\u26A0\uFE0F Import process failed, falling back to file URL:",i.message),console.error("Import error details:",i),{success:!0,title:s,url:n}}}catch(r){return console.error("Share markdown error:",r),{success:!1,error:r.message}}}async shareMarkdown(s,e,t){try{if(t&&t.setMessage("\u{1F50D} \u6B63\u5728\u68C0\u67E5\u6388\u6743\u72B6\u6001..."),!await this.ensureValidTokenWithReauth(t))throw new Error("\u6388\u6743\u5931\u6548\u4E14\u91CD\u65B0\u6388\u6743\u5931\u8D25\uFF0C\u8BF7\u624B\u52A8\u91CD\u65B0\u6388\u6743");t&&t.setMessage("\u{1F4E4} \u6B63\u5728\u4E0A\u4F20\u6587\u4EF6\u5230\u98DE\u4E66...");let o=await this.uploadMarkdownFile(s,e);if(!o.success)throw new Error(o.error||"\u6587\u4EF6\u4E0A\u4F20\u5931\u8D25");if(!o.fileToken)throw new Error("\u6587\u4EF6\u4E0A\u4F20\u6210\u529F\u4F46\u672A\u83B7\u53D6\u5230\u6587\u4EF6\u4EE4\u724C");let n=`https://feishu.cn/file/${o.fileToken}`;t&&t.setMessage("\u{1F504} \u6B63\u5728\u8F6C\u6362\u4E3A\u98DE\u4E66\u6587\u6863...");try{let i=s.endsWith(".md")?s.slice(0,-3):s,a=await this.createImportTaskWithCorrectFolder(o.fileToken,i);if(a.success&&a.ticket){console.log("Step 3: Waiting for import completion (15s timeout)...");let l=await this.waitForImportCompletionWithTimeout(a.ticket,15e3);if(l.success&&l.documentToken){let h=`https://feishu.cn/docx/${l.documentToken}`;if(this.settings.enableLinkShare)try{t&&t.setMessage("\u{1F517} \u6B63\u5728\u8BBE\u7F6E\u6587\u6863\u5206\u4EAB\u6743\u9650..."),await this.setDocumentSharePermissions(l.documentToken),console.log("\u2705 Document share permissions set successfully")}catch(c){console.warn("\u26A0\uFE0F Failed to set document share permissions:",c)}try{await this.deleteSourceFile(o.fileToken)}catch(c){console.warn("\u26A0\uFE0F Failed to delete source file:",c.message)}return{success:!0,title:i,url:h}}else return console.warn("\u26A0\uFE0F Import task failed or timed out, falling back to file URL"),console.warn("Final result details:",l),{success:!0,title:s,url:n}}else return console.warn("\u26A0\uFE0F Failed to create import task, falling back to file URL"),console.warn("Import result details:",a),{success:!0,title:s,url:n}}catch(i){return console.warn("\u26A0\uFE0F Import process failed, falling back to file URL:",i.message),console.error("Import error details:",i),{success:!0,title:s,url:n}}}catch(r){return console.error("Share markdown error:",r),{success:!1,error:r.message}}}async getFolderList(s){try{if(!await this.ensureValidToken())throw new Error("Token\u65E0\u6548\uFF0C\u8BF7\u91CD\u65B0\u6388\u6743");let t=`${p.BASE_URL}/drive/v1/files`,r=new URLSearchParams({folder_token:s||"",page_size:"50"}),o=await(0,u.requestUrl)({url:`${t}?${r.toString()}`,method:"GET",headers:{Authorization:`Bearer ${this.settings.accessToken}`,"Content-Type":"application/json"}}),n=o.json||JSON.parse(o.text);if(n.code===0)return{code:0,data:{folders:n.data.files.filter(a=>a.type==="folder").map(a=>({...a,folder_token:a.token,token:a.token})),has_more:n.data.has_more}};throw new Error(n.msg||"\u83B7\u53D6\u6587\u4EF6\u5939\u5217\u8868\u5931\u8D25")}catch(e){throw console.error("Get folder list error:",e),e}}async uploadMarkdownFile(s,e){try{if(!await this.ensureValidToken())throw new Error("Token\u65E0\u6548\uFF0C\u8BF7\u91CD\u65B0\u6388\u6743");let r="---7MA4YWxkTrZu0gW",o=s.endsWith(".md")?s:`${s}.md`,n=new TextEncoder().encode(e),i=n.length,a=[];a.push(`--${r}`),a.push('Content-Disposition: form-data; name="file_name"'),a.push(""),a.push(o),a.push(`--${r}`),a.push('Content-Disposition: form-data; name="parent_type"'),a.push(""),a.push("explorer"),a.push(`--${r}`),a.push('Content-Disposition: form-data; name="size"'),a.push(""),a.push(i.toString()),this.settings.defaultFolderId&&this.settings.defaultFolderId!==""&&this.settings.defaultFolderId!=="nodcn2EG5YG1i5Rsh5uZs0FsUje"&&(a.push(`--${r}`),a.push('Content-Disposition: form-data; name="parent_node"'),a.push(""),a.push(this.settings.defaultFolderId)),a.push(`--${r}`),a.push(`Content-Disposition: form-data; name="file"; filename="${o}"`),a.push("Content-Type: text/markdown"),a.push("");let l=a.join(`\r
`)+`\r
`,h=`\r
--${r}--\r
`,c=new TextEncoder().encode(l),k=new TextEncoder().encode(h),b=c.length+n.length+k.length,x=new Uint8Array(b),y=0;x.set(c,y),y+=c.length,x.set(n,y),y+=n.length,x.set(k,y);let m=await(0,u.requestUrl)({url:p.UPLOAD_URL,method:"POST",headers:{Authorization:`Bearer ${this.settings.accessToken}`,"Content-Type":`multipart/form-data; boundary=${r}`},body:x.buffer}),F=m.json||JSON.parse(m.text);if(F.code===0){let T=`https://feishu.cn/file/${F.data.file_token}`;return{success:!0,fileToken:F.data.file_token,url:T}}else{let T=B[F.code]||F.msg||"\u4E0A\u4F20\u5931\u8D25";return console.error("Upload failed:",F),{success:!1,error:T}}}catch(t){return console.error("Upload file error:",t),{success:!1,error:t.message}}}async refreshAccessToken(){try{if(!this.settings.refreshToken)return!1;let s=await(0,u.requestUrl)({url:p.REFRESH_TOKEN_URL,method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({grant_type:"refresh_token",refresh_token:this.settings.refreshToken})}),e=s.json||JSON.parse(s.text);return e.code===0?(this.settings.accessToken=e.data.access_token,this.settings.refreshToken=e.data.refresh_token,!0):(console.error("Token refresh failed:",e),!1)}catch(s){return console.error("Token refresh error:",s),!1}}generateRandomState(){return Math.random().toString(36).substring(2,15)+Math.random().toString(36).substring(2,15)}async ensureValidToken(){if(!this.settings.accessToken)return!1;try{let s=await(0,u.requestUrl)({url:p.USER_INFO_URL,method:"GET",headers:{Authorization:`Bearer ${this.settings.accessToken}`}}),e=s.json||JSON.parse(s.text);return e.code===0?!0:e.code===99991664?await this.refreshAccessToken():!1}catch(s){return console.error("Token validation error:",s),!1}}async ensureValidTokenWithReauth(s){if(!this.settings.accessToken)return await this.triggerReauth("\u6CA1\u6709\u8BBF\u95EE\u4EE4\u724C",s);try{let e=await(0,u.requestUrl)({url:p.USER_INFO_URL,method:"GET",headers:{Authorization:`Bearer ${this.settings.accessToken}`}}),t=e.json||JSON.parse(e.text);return t.code===0?!0:this.isTokenExpiredError(t.code)?await this.refreshAccessToken()?!0:!!await this.triggerReauth("Token\u5237\u65B0\u5931\u8D25",s):!!await this.triggerReauth(`Token\u65E0\u6548 (\u9519\u8BEF\u7801: ${t.code})`,s)}catch(e){return console.error("Token\u9A8C\u8BC1\u51FA\u9519:",e),!!await this.triggerReauth("Token\u9A8C\u8BC1\u51FA\u9519",s)}}isTokenExpiredError(s){return[99991664,99991663,99991665,99991666,1].includes(s)}async triggerReauth(s,e){e?e.setMessage(`\u{1F504} ${s}\uFF0C\u6B63\u5728\u81EA\u52A8\u91CD\u65B0\u6388\u6743...`):new u.Notice(`\u{1F504} ${s}\uFF0C\u6B63\u5728\u81EA\u52A8\u91CD\u65B0\u6388\u6743...`);try{if(!this.settings.appId||!this.settings.appSecret){let r="\u274C \u5E94\u7528\u914D\u7F6E\u4E0D\u5B8C\u6574\uFF0C\u8BF7\u5728\u8BBE\u7F6E\u4E2D\u914D\u7F6E App ID \u548C App Secret";return e?(e.setMessage(r),setTimeout(()=>e.hide(),3e3)):new u.Notice(r),!1}let t=this.generateAuthUrl();return window.open(t,"_blank"),e?e.setMessage("\u{1F310} \u5DF2\u6253\u5F00\u6D4F\u89C8\u5668\u8FDB\u884C\u91CD\u65B0\u6388\u6743\uFF0C\u5B8C\u6210\u540E\u5C06\u81EA\u52A8\u7EE7\u7EED\u5206\u4EAB..."):new u.Notice("\u{1F310} \u5DF2\u6253\u5F00\u6D4F\u89C8\u5668\u8FDB\u884C\u91CD\u65B0\u6388\u6743\uFF0C\u5B8C\u6210\u540E\u5C06\u81EA\u52A8\u7EE7\u7EED\u5206\u4EAB..."),await this.waitForReauth(e)}catch(t){return console.error("\u91CD\u65B0\u6388\u6743\u5931\u8D25:",t),new u.Notice(`\u274C \u91CD\u65B0\u6388\u6743\u5931\u8D25: ${t.message}`),!1}}async waitForReauth(s){return new Promise(e=>{let t=setTimeout(()=>{window.removeEventListener("feishu-auth-success",r);let o="\u23F0 \u6388\u6743\u7B49\u5F85\u8D85\u65F6\uFF0C\u8BF7\u624B\u52A8\u91CD\u8BD5\u5206\u4EAB";s?(s.setMessage(o),setTimeout(()=>s.hide(),3e3)):new u.Notice(o),e(!1)},3e5),r=()=>{clearTimeout(t),window.removeEventListener("feishu-auth-success",r),s&&s.setMessage("\u2705 \u6388\u6743\u6210\u529F\uFF0C\u6B63\u5728\u7EE7\u7EED\u5206\u4EAB..."),setTimeout(()=>{e(!0)},1e3)};window.addEventListener("feishu-auth-success",r)})}async createImportTaskWithCorrectFolder(s,e){try{let t={file_extension:"md",file_token:s,type:"docx",file_name:e,point:{mount_type:1,mount_key:this.settings.defaultFolderId||"nodcn2EG5YG1i5Rsh5uZs0FsUje"}},r=await(0,u.requestUrl)({url:`${p.BASE_URL}/drive/v1/import_tasks`,method:"POST",headers:{Authorization:`Bearer ${this.settings.accessToken}`,"Content-Type":"application/json"},body:JSON.stringify(t)}),o=r.json||JSON.parse(r.text);return o.code===0?{success:!0,ticket:o.data.ticket}:{success:!1,error:o.msg||"\u521B\u5EFA\u5BFC\u5165\u4EFB\u52A1\u5931\u8D25"}}catch(t){return console.error("Create import task error:",t),{success:!1,error:t.message}}}async waitForImportCompletionWithTimeout(s,e){let t=Date.now(),r=25;for(let o=1;o<=r;o++){let n=Date.now()-t;if(n>=e)return console.warn(`Import timeout after ${n}ms`),{success:!1,error:`\u5BFC\u5165\u4EFB\u52A1\u8D85\u65F6 (${e}ms)`};try{let i=await this.checkImportStatus(s);if(i.success&&(i.status===3||i.status===0)){if(i.documentToken)return{success:!0,documentToken:i.documentToken};console.warn("Import completed but no document token returned, continuing to wait...")}else if(i.success&&i.status===2){if(console.log(`\u{1F50D} Status 2 detected. Document token: ${i.documentToken||"none"}`),i.documentToken)return console.log(`\u2705 Import completed despite failure status, got document token: ${i.documentToken}`),{success:!0,documentToken:i.documentToken};if(console.warn(`\u26A0\uFE0F Import shows failure status (${i.status}), no document token yet. Attempt ${o}/8, continuing to wait...`),!(o<=8))return console.error("\u274C Import failed after extended waiting"),{success:!1,error:"\u5BFC\u5165\u4EFB\u52A1\u5931\u8D25"}}else console.log(`\u{1F4CA} Other status: ${i.status}, success: ${i.success}`);if(o<r){let a=this.getDelayForAttempt(o);await new Promise(l=>setTimeout(l,a))}}catch(i){console.error("Check import status error:",i);let a=this.getDelayForAttempt(o);await new Promise(l=>setTimeout(l,a))}}return{success:!1,error:"\u5BFC\u5165\u4EFB\u52A1\u8D85\u65F6"}}getDelayForAttempt(s){return s<=3?1e3:s<=8?2e3:3e3}async checkImportStatus(s){try{let e=await(0,u.requestUrl)({url:`${p.BASE_URL}/drive/v1/import_tasks/${s}`,method:"GET",headers:{Authorization:`Bearer ${this.settings.accessToken}`,"Content-Type":"application/json"}}),t=e.json||JSON.parse(e.text);if(t.code===0){let r=t.data.result;return{success:!0,status:r.job_status,documentToken:r.token}}else return console.error("\u274C Import status check failed:",t),{success:!1,error:t.msg||"\u68C0\u67E5\u5BFC\u5165\u72B6\u6001\u5931\u8D25"}}catch(e){return console.error("Check import status error:",e),{success:!1,error:e.message}}}async deleteSourceFile(s){try{let e;try{e=await(0,u.requestUrl)({url:`${p.BASE_URL}/drive/v1/files/${s}/trash`,method:"POST",headers:{Authorization:`Bearer ${this.settings.accessToken}`,"Content-Type":"application/json"},body:JSON.stringify({})})}catch(r){console.warn("\u26A0\uFE0F Trash method failed, trying direct delete..."),e=await(0,u.requestUrl)({url:`${p.BASE_URL}/drive/v1/files/${s}?type=file`,method:"DELETE",headers:{Authorization:`Bearer ${this.settings.accessToken}`,"Content-Type":"application/json"}})}if(e.status!==200)throw new Error(`\u5220\u9664\u8BF7\u6C42\u5931\u8D25\uFF0C\u72B6\u6001\u7801: ${e.status}`);let t=e.json||JSON.parse(e.text);t.code!==0&&(console.warn("\u26A0\uFE0F Delete API returned non-zero code:",t.code,t.msg),console.log("\u{1F4DD} Source file deletion completed (may have been moved to trash)"))}catch(e){console.error("\u274C Delete source file error:",e)}}async findPlaceholderBlocks(s,e){try{let t=[],r="",o=!0,n=this.compilePlaceholderPatterns(e),i=new Set(e.map(a=>a.placeholder));for(console.log(`\u{1F50D} Searching for ${i.size} placeholders in document...`);o&&i.size>0;){let a=new URLSearchParams({page_size:"500"});r&&a.append("page_token",r);let l=await(0,u.requestUrl)({url:`${p.BASE_URL}/docx/v1/documents/${s}/blocks?${a.toString()}`,method:"GET",headers:{Authorization:`Bearer ${this.settings.accessToken}`,"Content-Type":"application/json"}}),h=l.json||JSON.parse(l.text);if(h.code!==0)throw new Error(h.msg||"\u83B7\u53D6\u6587\u6863\u5757\u5931\u8D25");let c=this.searchPlaceholdersInBlocks(h.data.items,n,i);if(t.push(...c),i.size===0){console.log(`\u2705 All ${e.length} placeholders found, stopping search early`);break}o=h.data.has_more,r=h.data.page_token}return console.log(`\u{1F3AF} Found ${t.length}/${e.length} placeholder blocks`),t}catch(t){throw console.error("Find placeholder blocks error:",t),t}}compilePlaceholderPatterns(s){let e=new Map;return s.forEach(t=>{let r=t.placeholder,o=r.replace(/^__/,"").replace(/__$/,""),n=[new RegExp(this.escapeRegExp(r)),new RegExp(this.escapeRegExp(`!${o}`)),new RegExp(this.escapeRegExp(o))];e.set(r,{fileInfo:t,patterns:n})}),e}searchPlaceholdersInBlocks(s,e,t){let r=[];for(let o=0;o<s.length;o++){let n=s[o];if(!n.text||!n.text.elements)continue;let i=this.extractBlockTextContent(n);if(this.hasPlaceholderFeatures(i))for(let a of t){let l=e.get(a);if(!l)continue;if(l.patterns.some(c=>c.test(i))&&(console.log(`\u2705 Found placeholder: "${a}" in block ${n.block_id}`),r.push({blockId:n.block_id,parentId:n.parent_id,index:o,placeholder:a,fileInfo:l.fileInfo}),t.delete(a),t.size===0))return r}}return r}extractBlockTextContent(s){return s.text.elements.filter(e=>e.text_run&&e.text_run.content).map(e=>e.text_run.content).join("")}hasPlaceholderFeatures(s){return s.includes("FEISHU_FILE_")||s.includes("__FEISHU_FILE_")}escapeRegExp(s){return s.replace(/[.*+?^${}()|[\]\\]/g,"\\$&")}async insertFileBlock(s,e){try{let t=e.fileInfo.isImage?27:23,r=e.fileInfo.isImage?{image:{}}:{file:{}},o={index:e.index,children:[{block_type:t,...r}]},n=await(0,u.requestUrl)({url:`${p.BASE_URL}/docx/v1/documents/${s}/blocks/${e.parentId}/children`,method:"POST",headers:{Authorization:`Bearer ${this.settings.accessToken}`,"Content-Type":"application/json"},body:JSON.stringify(o)}),i=n.json||JSON.parse(n.text);if(i.code!==0)throw new Error(i.msg||"\u63D2\u5165\u6587\u4EF6\u5757\u5931\u8D25");let a=i.data.children[0],l=a.block_id;return!e.fileInfo.isImage&&a.block_type===33&&(a.children&&a.children.length>0?l=a.children[0]:console.warn("\u26A0\uFE0F View Block created but no child File Block found")),l}catch(t){throw console.error("Insert file block error:",t),t}}async uploadFileToDocument(s,e,t,r){try{if(!await this.ensureValidToken())throw new Error("Token\u65E0\u6548\uFF0C\u8BF7\u91CD\u65B0\u6388\u6743");let n="---7MA4YWxkTrZu0gW",i=t.isImage?"docx_image":"docx_file",a=r.byteLength,l=[];l.push(`--${n}`),l.push('Content-Disposition: form-data; name="file_name"'),l.push(""),l.push(t.fileName),l.push(`--${n}`),l.push('Content-Disposition: form-data; name="parent_type"'),l.push(""),l.push(i),l.push(`--${n}`),l.push('Content-Disposition: form-data; name="parent_node"'),l.push(""),l.push(e),l.push(`--${n}`),l.push('Content-Disposition: form-data; name="size"'),l.push(""),l.push(a.toString()),l.push(`--${n}`),l.push('Content-Disposition: form-data; name="extra"'),l.push(""),l.push(`{"drive_route_token":"${s}"}`),l.push(`--${n}`),l.push('Content-Disposition: form-data; name="file"'),l.push("Content-Type: application/octet-stream"),l.push("");let h=l.join(`\r
`)+`\r
`,c=`\r
--${n}--\r
`,k=new TextEncoder().encode(h),b=new TextEncoder().encode(c),x=k.length+a+b.length,y=new Uint8Array(x),m=0;y.set(k,m),m+=k.length,y.set(new Uint8Array(r),m),m+=a,y.set(b,m);let F=await(0,u.requestUrl)({url:p.UPLOAD_URL,method:"POST",headers:{Authorization:`Bearer ${this.settings.accessToken}`,"Content-Type":`multipart/form-data; boundary=${n}`},body:y.buffer}),T=F.json||JSON.parse(F.text);if(T.code===0)return console.log(`\u2705 Uploaded ${t.isImage?"image":"file"} material: ${T.data.file_token}`),T.data.file_token;{let $=B[T.code]||T.msg||"\u4E0A\u4F20\u6587\u4EF6\u7D20\u6750\u5931\u8D25";throw new Error($)}}catch(o){throw console.error("Upload file to document error:",o),o}}async setFileBlockContent(s,e,t,r){try{let o=r?{replace_image:{token:t}}:{replace_file:{token:t}};console.log(`\u{1F527} Setting ${r?"image":"file"} block content:`,{documentId:s,blockId:e,fileToken:t,requestData:o});let n=await(0,u.requestUrl)({url:`${p.BASE_URL}/docx/v1/documents/${s}/blocks/${e}`,method:"PATCH",headers:{Authorization:`Bearer ${this.settings.accessToken}`,"Content-Type":"application/json"},body:JSON.stringify(o)});console.log(`\u{1F4CB} Set block content response status: ${n.status}`);let i=n.json||JSON.parse(n.text);if(console.log("\u{1F4CB} Set block content response:",i),i.code!==0)throw new Error(i.msg||"\u8BBE\u7F6E\u6587\u4EF6\u5757\u5185\u5BB9\u5931\u8D25");console.log(`\u2705 Set ${r?"image":"file"} block content: ${e}`)}catch(o){throw console.error("Set file block content error:",o),o.message&&o.message.includes("400")&&(console.error("\u274C 400 Error details: This might be due to:"),console.error("  1. Invalid file token or block ID"),console.error("  2. File type not supported for this block type"),console.error("  3. Block already has content"),console.error("  4. API parameter format issue")),o}}async replacePlaceholderWithFileName(s,e,t){try{let r={update_text_elements:{elements:[{text_run:{content:`\u{1F4CE} ${t}`}}]}};console.log(`\u{1F527} Replacing placeholder with filename: ${t}`);let o=await(0,u.requestUrl)({url:`${p.BASE_URL}/docx/v1/documents/${s}/blocks/${e.blockId}`,method:"PATCH",headers:{Authorization:`Bearer ${this.settings.accessToken}`,"Content-Type":"application/json"},body:JSON.stringify(r)}),n=o.json||JSON.parse(o.text);console.log("\u{1F4CB} Replace with filename response:",n),n.code!==0?(console.warn(`\u26A0\uFE0F Failed to replace with filename: ${n.msg}, trying empty text...`),await this.replacePlaceholderText(s,e)):console.log(`\u2705 Replaced placeholder with filename: ${t}`)}catch(r){console.error("Replace placeholder with filename error:",r);try{await this.replacePlaceholderText(s,e)}catch(o){console.error("All replacement methods failed:",o)}}}async replacePlaceholderText(s,e){try{let t={update_text_elements:{elements:[{text_run:{content:""}}]}};console.log(`\u{1F527} Replacing placeholder text in block: ${e.blockId}`);let r=await(0,u.requestUrl)({url:`${p.BASE_URL}/docx/v1/documents/${s}/blocks/${e.blockId}`,method:"PATCH",headers:{Authorization:`Bearer ${this.settings.accessToken}`,"Content-Type":"application/json"},body:JSON.stringify(t)}),o=r.json||JSON.parse(r.text);console.log("\u{1F4CB} Replace placeholder response:",o),o.code!==0?(console.warn(`\u26A0\uFE0F Failed to replace placeholder text: ${o.msg}, trying delete method...`),await this.deletePlaceholderBlock(s,e)):console.log(`\u2705 Replaced placeholder text in block: ${e.blockId}`)}catch(t){console.error("Replace placeholder text error:",t);try{await this.deletePlaceholderBlock(s,e)}catch(r){console.error("Both replace and delete failed:",r)}}}async deletePlaceholderBlock(s,e){try{let t={start_index:e.index,end_index:e.index+1},r=await(0,u.requestUrl)({url:`${p.BASE_URL}/docx/v1/documents/${s}/blocks/${e.parentId}/children/batch_delete`,method:"DELETE",headers:{Authorization:`Bearer ${this.settings.accessToken}`,"Content-Type":"application/json"},body:JSON.stringify(t)}),o=r.json||JSON.parse(r.text);if(o.code!==0)throw new Error(o.msg||"\u5220\u9664\u5360\u4F4D\u7B26\u5757\u5931\u8D25");console.log(`\u2705 Deleted placeholder block: ${e.blockId}`)}catch(t){throw console.error("Delete placeholder block error:",t),t}}async readLocalFile(s){var e;try{let t=s.trim();t=t.replace(/^\.[\\/]/,"");let r=(0,u.normalizePath)(t);console.log(`\u{1F50D} Trying to read file: "${s}" -> "${r}"`);let o=this.app.vault.getFileByPath(r);if(!o){let i=this.app.vault.getFiles(),a=(e=r.split("/").pop())==null?void 0:e.toLowerCase();if(a){let l=i.find(h=>h.name.toLowerCase()===a);l&&(o=l,console.log(`\u2705 Found file by name: ${o.path}`))}}if(!o){console.warn(`\u274C File not found: ${r}`);let a=this.app.vault.getFiles().filter(l=>l.name.includes(r.split("/").pop()||""));return a.length>0&&console.log("\u{1F4CB} Similar files found:",a.map(l=>l.path)),null}let n=await this.app.vault.readBinary(o);return console.log(`\u2705 Successfully read file: ${o.path} (${n.byteLength} bytes)`),n}catch(t){return console.error(`\u274C Error reading local file ${s}:`,t),null}}async processFileUploads(s,e,t){if(e.length===0){console.log("\u{1F4DD} No local files to process");return}try{t&&t.setMessage(`\u{1F50D} \u6B63\u5728\u67E5\u627E\u5360\u4F4D\u7B26 (${e.length} \u4E2A\u6587\u4EF6)...`);let r=await this.findPlaceholderBlocks(s,e);if(r.length===0){console.warn("\u26A0\uFE0F No placeholder blocks found in document");return}console.log(`\u{1F3AF} Found ${r.length} placeholder blocks to process`);let o=this.sortPlaceholdersByOriginalOrder(r,e);console.log("\u{1F4CB} Sorted placeholder blocks by original order");for(let n=0;n<o.length;n++){let i=o[n],a=i.fileInfo;t&&t.setMessage(`\u{1F4E4} \u6B63\u5728\u4E0A\u4F20\u6587\u4EF6 ${n+1}/${o.length}: ${a.fileName}...`);try{let l=await this.readLocalFile(a.originalPath);if(!l){console.warn(`\u26A0\uFE0F Could not read file: ${a.originalPath}, skipping...`);continue}let h={...i,index:i.index+n};console.log(`\u{1F4CD} Adjusted insert position for ${a.fileName}: ${i.index} -> ${h.index}`);let c=await this.insertFileBlock(s,h),k=await this.uploadFileToDocument(s,c,a,l);await this.setFileBlockContent(s,c,k,a.isImage),console.log("\u23F3 Waiting 2 seconds for file content to be set..."),await new Promise(b=>setTimeout(b,2e3)),await this.replacePlaceholderText(s,i),console.log(`\u2705 Successfully processed file: ${a.fileName}`)}catch(l){console.error(`\u274C Failed to process file ${a.fileName}:`,l)}}console.log(`\u{1F389} File upload processing completed: ${o.length} files processed`)}catch(r){throw console.error("Process file uploads error:",r),r}}sortPlaceholdersByOriginalOrder(s,e){console.log("\u{1F4CB} Original localFiles order:"),e.forEach((o,n)=>{console.log(`  ${n}: ${o.fileName} -> ${o.placeholder}`)}),console.log("\u{1F4CB} Found placeholder blocks:"),s.forEach((o,n)=>{console.log(`  ${n}: ${o.fileInfo.fileName} -> ${o.placeholder} (index: ${o.index})`)});let t=new Map;e.forEach((o,n)=>{t.set(o.placeholder,n)});let r=s.sort((o,n)=>{var l,h;let i=(l=t.get(o.placeholder))!=null?l:999,a=(h=t.get(n.placeholder))!=null?h:999;return console.log(`\u{1F504} Comparing: ${o.fileInfo.fileName}(order:${i}, index:${o.index}) vs ${n.fileInfo.fileName}(order:${a}, index:${n.index})`),i!==a?i-a:o.index-n.index});return console.log("\u{1F4CB} Sorted placeholder blocks:"),r.forEach((o,n)=>{console.log(`  ${n}: ${o.fileInfo.fileName} -> ${o.placeholder}`)}),r}async processSubDocuments(s,e,t){console.log(`\u{1F680} Starting sub-document processing for ${e.length} documents`);for(let r=0;r<e.length;r++){let o=e[r];try{t&&t.setMessage(`\u{1F4C4} \u6B63\u5728\u5904\u7406\u5B50\u6587\u6863 ${r+1}/${e.length}: ${o.fileName}...`),console.log(`\u{1F4C4} Processing sub-document: ${o.fileName} (${o.originalPath})`);let n=await this.readSubDocumentContent(o.originalPath);if(!n){console.warn(`\u26A0\uFE0F Could not read sub-document: ${o.originalPath}, skipping...`);continue}let i=await this.uploadSubDocument(o.fileName,n);if(!i.success){console.warn(`\u26A0\uFE0F Failed to upload sub-document: ${o.fileName}, error: ${i.error}`);continue}await this.insertSubDocumentLink(s,o,i),console.log(`\u2705 Successfully processed sub-document: ${o.fileName}`)}catch(n){console.error(`\u274C Error processing sub-document ${o.fileName}:`,n)}}console.log("\u2705 Completed sub-document processing")}async readSubDocumentContent(s){var e;try{let t=s.trim(),r=(0,u.normalizePath)(t);console.log(`\u{1F50D} Reading sub-document: "${s}" -> "${r}"`);let o=this.app.vault.getFileByPath(r);if(!o){let i=this.app.vault.getMarkdownFiles(),a=(e=r.split("/").pop())==null?void 0:e.toLowerCase();if(a){let l=i.find(h=>h.name.toLowerCase()===a);l&&(o=l,console.log(`\u2705 Found sub-document by name: ${o.path}`))}}if(!o)return console.warn(`\u274C Sub-document not found: ${r}`),null;let n=await this.app.vault.read(o);return console.log(`\u2705 Successfully read sub-document: ${o.path} (${n.length} characters)`),n}catch(t){return console.error(`\u274C Error reading sub-document ${s}:`,t),null}}async uploadSubDocument(s,e){try{console.log(`\u{1F4E4} Uploading sub-document: ${s}`);let t=await this.uploadMarkdownFile(s,e);if(!t.success)return{success:!1,error:t.error||"\u5B50\u6587\u6863\u4E0A\u4F20\u5931\u8D25"};let r=s.endsWith(".md")?s.slice(0,-3):s,o=await this.createImportTaskWithCorrectFolder(t.fileToken,r);if(!o.success)return{success:!1,error:o.error||"\u5B50\u6587\u6863\u5BFC\u5165\u4EFB\u52A1\u521B\u5EFA\u5931\u8D25"};let n=await this.waitForImportCompletionWithTimeout(o.ticket,15e3);if(n.success&&n.documentToken){let i=`https://feishu.cn/docx/${n.documentToken}`;try{await this.deleteSourceFile(t.fileToken)}catch(a){console.warn("\u26A0\uFE0F Failed to delete sub-document source file:",a)}return{success:!0,documentToken:n.documentToken,url:i,title:r}}else return{success:!1,error:"\u5B50\u6587\u6863\u5BFC\u5165\u8D85\u65F6\u6216\u5931\u8D25"}}catch(t){return console.error("Upload sub-document error:",t),{success:!1,error:t.message}}}async insertSubDocumentLink(s,e,t){try{console.log(`\u{1F517} Inserting sub-document link for: ${e.fileName}`);let r=await this.findPlaceholderBlocks(s,[e]);if(r.length===0){console.warn(`\u26A0\uFE0F No placeholder found for sub-document: ${e.fileName}`);return}let o=r[0],n=`\u{1F4C4} [${t.title}](${t.url})`;await this.replaceTextInBlock(s,o.blockId,n),console.log(`\u2705 Successfully inserted sub-document link: ${e.fileName}`)}catch(r){console.error(`\u274C Error inserting sub-document link for ${e.fileName}:`,r)}}async replaceTextInBlock(s,e,t){try{let r={update_text_elements:{elements:[{text_run:{content:t}}]}};console.log(`\u{1F527} Replacing text in block ${e} with: "${t}"`);let o=await(0,u.requestUrl)({url:`${p.BASE_URL}/docx/v1/documents/${s}/blocks/${e}`,method:"PATCH",headers:{Authorization:`Bearer ${this.settings.accessToken}`,"Content-Type":"application/json"},body:JSON.stringify(r)}),n=o.json||JSON.parse(o.text);if(console.log("\u{1F4CB} Replace text response:",n),n.code!==0)throw new Error(n.msg||"\u66FF\u6362\u6587\u672C\u5931\u8D25");console.log(`\u2705 Successfully replaced text in block: ${e}`)}catch(r){throw console.error(`\u274C Error replacing text in block ${e}:`,r),r}}async setDocumentSharePermissions(s){try{if(!await this.ensureValidToken())throw new Error("Token\u65E0\u6548\uFF0C\u8BF7\u91CD\u65B0\u6388\u6743");try{let n=await this.getDocumentPermissions(s);console.log("\u{1F4CB} Current document permissions:",n)}catch(n){console.warn("\u26A0\uFE0F Failed to get current permissions:",n)}let t={};this.settings.enableLinkShare&&(t.link_share_entity=this.settings.linkSharePermission,this.settings.linkSharePermission==="anyone_readable"||this.settings.linkSharePermission,t.external_access_entity="open",t.share_entity="anyone",t.manage_collaborator_entity="collaborator_can_view"),console.log(`\u{1F527} Setting document share permissions for ${s}:`,t);let r=await(0,u.requestUrl)({url:`${p.BASE_URL}/drive/v2/permissions/${s}/public?type=docx`,method:"PATCH",headers:{Authorization:`Bearer ${this.settings.accessToken}`,"Content-Type":"application/json"},body:JSON.stringify(t)});console.log(`\u{1F4CB} Set document permissions response status: ${r.status}`);let o;try{o=r.json||JSON.parse(r.text)}catch(n){throw console.error("\u274C Failed to parse response:",r.text),new Error(`API\u54CD\u5E94\u89E3\u6790\u5931\u8D25: ${r.status} - ${r.text}`)}if(console.log("\u{1F4CB} Set document permissions response data:",o),o.code!==0)throw console.error("\u274C API returned error:",{code:o.code,msg:o.msg,requestData:t,documentToken:s}),new Error(`\u8BBE\u7F6E\u6587\u6863\u5206\u4EAB\u6743\u9650\u5931\u8D25 (${o.code}): ${o.msg}`);console.log(`\u2705 Successfully set document share permissions for ${s}`)}catch(e){throw console.error("Set document share permissions error:",e),e}}async getDocumentPermissions(s){try{if(!await this.ensureValidToken())throw new Error("Token\u65E0\u6548\uFF0C\u8BF7\u91CD\u65B0\u6388\u6743");let t=await(0,u.requestUrl)({url:`${p.BASE_URL}/drive/v2/permissions/${s}/public?type=docx`,method:"GET",headers:{Authorization:`Bearer ${this.settings.accessToken}`,"Content-Type":"application/json"}}),r=t.json||JSON.parse(t.text);if(r.code!==0)throw new Error(r.msg||"\u83B7\u53D6\u6587\u6863\u6743\u9650\u8BBE\u7F6E\u5931\u8D25");return r.data.permission_public}catch(e){throw console.error("Get document permissions error:",e),e}}async verifyDocumentLinkSharing(s){try{let e=await this.getDocumentPermissions(s);console.log("\u{1F50D} Analyzing document permissions:",e);let t=e.link_share_entity,r=e.external_access_entity,o=!1,n="none",i="none",a="";t==="close"?a="\u94FE\u63A5\u5206\u4EAB\u5DF2\u5173\u95ED\uFF0C\u53EA\u6709\u534F\u4F5C\u8005\u53EF\u4EE5\u8BBF\u95EE\u6587\u6863":t==="tenant_readable"?(o=!0,n="tenant",i="readable",a="\u7EC4\u7EC7\u5185\u83B7\u5F97\u94FE\u63A5\u7684\u4EBA\u53EF\u4EE5\u9605\u8BFB\u6587\u6863"):t==="tenant_editable"?(o=!0,n="tenant",i="editable",a="\u7EC4\u7EC7\u5185\u83B7\u5F97\u94FE\u63A5\u7684\u4EBA\u53EF\u4EE5\u7F16\u8F91\u6587\u6863"):t==="anyone_can_view"&&r==="open"?(o=!0,n="internet",i="readable",a="\u4E92\u8054\u7F51\u4E0A\u83B7\u5F97\u94FE\u63A5\u7684\u4EFB\u4F55\u4EBA\u90FD\u53EF\u4EE5\u9605\u8BFB\u6587\u6863"):t==="anyone_can_edit"&&r==="open"?(o=!0,n="internet",i="editable",a="\u4E92\u8054\u7F51\u4E0A\u83B7\u5F97\u94FE\u63A5\u7684\u4EFB\u4F55\u4EBA\u90FD\u53EF\u4EE5\u7F16\u8F91\u6587\u6863"):a=`\u672A\u77E5\u7684\u94FE\u63A5\u5206\u4EAB\u8BBE\u7F6E: ${t}, external_access: ${r}`;let l={isLinkSharingEnabled:o,shareScope:n,accessLevel:i,explanation:a};return console.log("\u{1F4CA} Link sharing analysis result:",l),l}catch(e){throw console.error("Verify document link sharing error:",e),e}}};var d=require("obsidian");var w=require("obsidian"),_=class extends w.Modal{constructor(e,t,r){super(e);this.feishuApi=t,this.onSuccess=r}onOpen(){let{contentEl:e}=this;e.empty(),e.createEl("h2",{text:"\u{1F510} \u98DE\u4E66\u624B\u52A8\u6388\u6743"});let t=e.createDiv("setting-item-description");t.style.marginBottom="20px",t.innerHTML=`
			<p><strong>\u{1F680} \u7B80\u5316\u6388\u6743\u6D41\u7A0B - \u53EA\u9700\u590D\u5236\u7C98\u8D34URL\uFF1A</strong></p>
			<ol>
				<li>\u70B9\u51FB\u4E0B\u65B9\u7684"\u6253\u5F00\u6388\u6743\u9875\u9762"\u6309\u94AE</li>
				<li>\u5728\u5F39\u51FA\u7684\u98DE\u4E66\u9875\u9762\u4E2D\u767B\u5F55\u5E76\u786E\u8BA4\u6388\u6743</li>
				<li>\u6388\u6743\u6210\u529F\u540E\uFF0C\u4F1A\u8DF3\u8F6C\u5230\u4E00\u4E2A\u663E\u793A\u9519\u8BEF\u7684\u9875\u9762\uFF08\u8FD9\u662F\u6B63\u5E38\u7684\uFF09</li>
				<li><strong>\u590D\u5236\u6D4F\u89C8\u5668\u5730\u5740\u680F\u7684\u5B8C\u6574URL</strong>\uFF08\u5305\u542B code= \u53C2\u6570\uFF09</li>
				<li>\u5C06\u5B8C\u6574URL\u7C98\u8D34\u5230\u4E0B\u65B9\u8F93\u5165\u6846\u4E2D</li>
				<li>\u70B9\u51FB"\u5B8C\u6210\u6388\u6743"\u6309\u94AE</li>
			</ol>
			<div style="background: var(--background-modifier-success); padding: 10px; border-radius: 4px; margin-top: 10px;">
				<strong>\u{1F4A1} \u63D0\u793A\uFF1A</strong>\u65E0\u9700\u624B\u52A8\u63D0\u53D6\u6388\u6743\u7801\uFF0C\u76F4\u63A5\u590D\u5236\u5B8C\u6574\u7684\u56DE\u8C03URL\u5373\u53EF\uFF01
			</div>
		`,new w.Setting(e).setName("\u7B2C\u4E00\u6B65\uFF1A\u6253\u5F00\u6388\u6743\u9875\u9762").setDesc("\u70B9\u51FB\u6309\u94AE\u5728\u6D4F\u89C8\u5668\u4E2D\u6253\u5F00\u98DE\u4E66\u6388\u6743\u9875\u9762").addButton(o=>{o.setButtonText("\u{1F310} \u6253\u5F00\u6388\u6743\u9875\u9762").setCta().onClick(()=>{try{let n=this.feishuApi.generateAuthUrl();window.open(n,"_blank"),new w.Notice("\u2705 \u6388\u6743\u9875\u9762\u5DF2\u6253\u5F00\uFF0C\u8BF7\u5728\u6D4F\u89C8\u5668\u4E2D\u5B8C\u6210\u6388\u6743")}catch(n){new w.Notice(`\u274C \u751F\u6210\u6388\u6743\u94FE\u63A5\u5931\u8D25: ${n.message}`)}})});let r="";new w.Setting(e).setName("\u7B2C\u4E8C\u6B65\uFF1A\u7C98\u8D34\u56DE\u8C03URL").setDesc("\u4ECE\u6D4F\u89C8\u5668\u5730\u5740\u680F\u590D\u5236\u5B8C\u6574\u7684\u56DE\u8C03URL\u5E76\u7C98\u8D34\u5230\u6B64\u5904").addTextArea(o=>{o.setPlaceholder("\u7C98\u8D34\u5B8C\u6574\u7684\u56DE\u8C03URL\uFF0C\u4F8B\u5982\uFF1Ahttps://example.com/callback?code=xxx&state=xxx").setValue(r).onChange(n=>{r=n.trim()}),o.inputEl.style.width="100%",o.inputEl.style.height="80px"}),new w.Setting(e).setName("\u7B2C\u4E09\u6B65\uFF1A\u5B8C\u6210\u6388\u6743").setDesc("\u89E3\u6790\u56DE\u8C03URL\u5E76\u5B8C\u6210\u6388\u6743\u6D41\u7A0B").addButton(o=>{o.setButtonText("\u2705 \u5B8C\u6210\u6388\u6743").setCta().onClick(async()=>{await this.processCallback(r)})}),new w.Setting(e).addButton(o=>{o.setButtonText("\u53D6\u6D88").onClick(()=>{this.close()})})}async processCallback(e){try{if(!e){new w.Notice("\u274C \u8BF7\u5148\u7C98\u8D34\u56DE\u8C03URL");return}let t=new URL(e),r=t.searchParams.get("code"),o=t.searchParams.get("state");if(!r){new w.Notice("\u274C \u56DE\u8C03URL\u4E2D\u672A\u627E\u5230\u6388\u6743\u7801\uFF0C\u8BF7\u68C0\u67E5URL\u662F\u5426\u5B8C\u6574");return}let n=localStorage.getItem("feishu-oauth-state");if(n&&o!==n){new w.Notice("\u274C \u72B6\u6001\u9A8C\u8BC1\u5931\u8D25\uFF0C\u8BF7\u91CD\u65B0\u6388\u6743");return}new w.Notice("\u{1F504} \u6B63\u5728\u5904\u7406\u6388\u6743..."),await this.feishuApi.handleOAuthCallback(r)?(new w.Notice("\u{1F389} \u6388\u6743\u6210\u529F\uFF01"),this.onSuccess(),this.close()):new w.Notice("\u274C \u6388\u6743\u5904\u7406\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5")}catch(t){console.error("Process callback error:",t),new w.Notice(`\u274C \u5904\u7406\u6388\u6743\u65F6\u53D1\u751F\u9519\u8BEF: ${t.message}`)}}onClose(){let{contentEl:e}=this;e.empty()}};var j=require("obsidian"),C=class extends j.Modal{constructor(e,t,r){super(e);this.folders=[];this.currentPath=[];this.loading=!1;this.feishuApi=t,this.onSelect=r}onOpen(){let{contentEl:e}=this;e.empty(),e.createEl("h2",{text:"\u9009\u62E9\u6587\u4EF6\u5939"}),this.createBreadcrumb(e);let t=e.createDiv("folder-list-container");t.style.cssText=`
			max-height: 400px;
			overflow-y: auto;
			border: 1px solid var(--background-modifier-border);
			border-radius: 8px;
			margin: 16px 0;
		`;let r=e.createDiv("button-container");r.style.cssText=`
			display: flex;
			justify-content: space-between;
			margin-top: 16px;
		`;let o=r.createEl("button",{text:"\u9009\u62E9\u5F53\u524D\u6587\u4EF6\u5939",cls:"mod-cta"});o.onclick=()=>{let i=this.currentPath.length>0?this.currentPath[this.currentPath.length-1]:null;this.onSelect(i),this.close()};let n=r.createEl("button",{text:"\u53D6\u6D88"});n.onclick=()=>{this.close()},this.loadFolders(t)}createBreadcrumb(e){let t=e.createDiv("folder-breadcrumb");t.style.cssText=`
			display: flex;
			align-items: center;
			gap: 8px;
			margin: 16px 0;
			padding: 8px 12px;
			background: var(--background-secondary);
			border-radius: 6px;
			font-size: 14px;
		`;let r=t.createSpan("breadcrumb-item");r.textContent="\u6211\u7684\u7A7A\u95F4",r.style.cssText=`
			cursor: pointer;
			color: var(--text-accent);
			text-decoration: underline;
		`,r.onclick=()=>this.navigateToRoot(),this.currentPath.forEach((o,n)=>{t.createSpan("breadcrumb-separator").textContent=" / ";let i=t.createSpan("breadcrumb-item");i.textContent=o.name,n<this.currentPath.length-1?(i.style.cssText=`
					cursor: pointer;
					color: var(--text-accent);
					text-decoration: underline;
				`,i.onclick=()=>this.navigateToFolder(n)):i.style.cssText=`
					font-weight: bold;
					color: var(--text-normal);
				`})}async loadFolders(e){if(this.loading)return;this.loading=!0,e.empty();let t=e.createDiv("loading-indicator");t.textContent="\u6B63\u5728\u52A0\u8F7D\u6587\u4EF6\u5939...",t.style.cssText=`
			text-align: center;
			padding: 20px;
			color: var(--text-muted);
		`;try{let r=this.currentPath.length>0?this.currentPath[this.currentPath.length-1].folder_token||this.currentPath[this.currentPath.length-1].token:void 0,o=await this.feishuApi.getFolderList(r);this.folders=o.data.folders,e.empty(),this.renderFolderList(e)}catch(r){console.error("Failed to load folders:",r),e.empty();let o=e.createDiv("error-message");o.textContent=`\u52A0\u8F7D\u5931\u8D25: ${r.message}`,o.style.cssText=`
				text-align: center;
				padding: 20px;
				color: var(--text-error);
			`}finally{this.loading=!1}}renderFolderList(e){if(this.folders.length===0){let t=e.createDiv("empty-message");t.textContent="\u6B64\u6587\u4EF6\u5939\u4E3A\u7A7A",t.style.cssText=`
				text-align: center;
				padding: 20px;
				color: var(--text-muted);
			`;return}this.folders.forEach(t=>{let r=e.createDiv("folder-item");r.style.cssText=`
				display: flex;
				align-items: center;
				padding: 12px 16px;
				cursor: pointer;
				border-bottom: 1px solid var(--background-modifier-border);
				transition: background-color 0.2s;
			`;let o=r.createSpan("folder-icon");o.textContent="\u{1F4C1}",o.style.cssText=`
				margin-right: 12px;
				font-size: 16px;
			`;let n=r.createSpan("folder-name");n.textContent=t.name,n.style.cssText=`
				flex: 1;
				font-size: 14px;
			`,r.onmouseenter=()=>{r.style.backgroundColor="var(--background-modifier-hover)"},r.onmouseleave=()=>{r.style.backgroundColor=""},r.onclick=()=>{this.enterFolder(t)}})}async enterFolder(e){let t=this.currentPath.findIndex(n=>(n.folder_token||n.token)===(e.folder_token||e.token));t>=0?this.currentPath=this.currentPath.slice(0,t+1):this.currentPath.push(e);let r=this.contentEl.querySelector(".folder-breadcrumb");r&&(r.remove(),this.createBreadcrumb(this.contentEl));let o=this.contentEl.querySelector(".folder-list-container");o&&await this.loadFolders(o)}async navigateToRoot(){this.currentPath=[];let e=this.contentEl.querySelector(".folder-breadcrumb");e&&(e.remove(),this.createBreadcrumb(this.contentEl));let t=this.contentEl.querySelector(".folder-list-container");t&&await this.loadFolders(t)}async navigateToFolder(e){this.currentPath=this.currentPath.slice(0,e+1);let t=this.contentEl.querySelector(".folder-breadcrumb");t&&(t.remove(),this.createBreadcrumb(this.contentEl));let r=this.contentEl.querySelector(".folder-list-container");r&&await this.loadFolders(r)}onClose(){let{contentEl:e}=this;e.empty()}};var A=class extends d.PluginSettingTab{constructor(e,t){super(e,t);this.plugin=t}display(){let{containerEl:e}=this;e.empty(),e.createEl("h2",{text:"\u98DE\u4E66\u5206\u4EAB\u8BBE\u7F6E"});let t=e.createDiv("setting-item-description");t.innerHTML=`
			<p>\u76F4\u8FDE\u98DE\u4E66API\uFF0C\u56DE\u8C03\u5730\u5740\u4EC5\u4E2D\u8F6C\u65E0\u8BB0\u5F55\u3002</p>
			<p><strong>\u7279\u70B9\uFF1A</strong>\u65E0\u4F9D\u8D56\u3001\u66F4\u5B89\u5168\u3001\u54CD\u5E94\u66F4\u5FEB</p>
		`,e.createEl("h3",{text:"\u{1F527} \u5E94\u7528\u914D\u7F6E"}),new d.Setting(e).setName("App ID").setDesc("\u98DE\u4E66\u5E94\u7528\u7684 App ID").addText(g=>g.setPlaceholder("\u8F93\u5165\u98DE\u4E66\u5E94\u7528\u7684 App ID").setValue(this.plugin.settings.appId).onChange(async v=>{this.plugin.settings.appId=v.trim(),await this.plugin.saveSettings()})),new d.Setting(e).setName("App Secret").setDesc("\u98DE\u4E66\u5E94\u7528\u7684 App Secret").addText(g=>{g.setPlaceholder("\u8F93\u5165\u98DE\u4E66\u5E94\u7528\u7684 App Secret").setValue(this.plugin.settings.appSecret).onChange(async v=>{this.plugin.settings.appSecret=v.trim(),await this.plugin.saveSettings()}),g.inputEl.type="password"}),new d.Setting(e).setName("OAuth\u56DE\u8C03\u5730\u5740").setDesc("obsidian\u9700web\u56DE\u8C03\u4E2D\u8F6C\uFF0C\u4F8B\u5982\uFF1Ahttps://md2feishu.xinqi.life/oauth-callback").addText(g=>g.setPlaceholder("https://md2feishu.xinqi.life/oauth-callback").setValue(this.plugin.settings.callbackUrl).onChange(async v=>{this.plugin.settings.callbackUrl=v.trim(),await this.plugin.saveSettings()})),e.createEl("h3",{text:"\u{1F510} \u6388\u6743\u7BA1\u7406"});let o=e.createDiv("setting-item").createDiv("setting-item-info");o.createDiv("setting-item-name").setText("\u6388\u6743\u72B6\u6001");let n=o.createDiv("setting-item-description");this.plugin.settings.userInfo?n.innerHTML=`
				<span style="color: var(--text-success);">\u2705 \u5DF2\u6388\u6743</span><br>
				<strong>\u7528\u6237\uFF1A</strong>${this.plugin.settings.userInfo.name}<br>
				<strong>\u90AE\u7BB1\uFF1A</strong>${this.plugin.settings.userInfo.email}
			`:n.innerHTML='<span style="color: var(--text-error);">\u274C \u672A\u6388\u6743</span>',new d.Setting(e).setName("\u{1F680} \u4E00\u952E\u6388\u6743\uFF08\u63A8\u8350\uFF09").setDesc("\u81EA\u52A8\u6253\u5F00\u6D4F\u89C8\u5668\u5B8C\u6210\u6388\u6743\uFF0C\u901A\u8FC7\u4E91\u7AEF\u56DE\u8C03\u81EA\u52A8\u8FD4\u56DE\u6388\u6743\u7ED3\u679C\uFF0C\u65E0\u9700\u624B\u52A8\u64CD\u4F5C").addButton(g=>{g.setButtonText("\u{1F680} \u4E00\u952E\u6388\u6743").setCta().onClick(()=>{this.startAutoAuth()})}),new d.Setting(e).setName("\u{1F4DD} \u624B\u52A8\u6388\u6743\uFF08\u5907\u7528\uFF09").setDesc("\u5982\u679C\u4E00\u952E\u6388\u6743\u9047\u5230\u95EE\u9898\uFF0C\u53EF\u4EE5\u4F7F\u7528\u4F20\u7EDF\u7684\u624B\u52A8\u590D\u5236\u7C98\u8D34\u6388\u6743\u65B9\u5F0F").addButton(g=>{g.setButtonText("\u624B\u52A8\u6388\u6743").onClick(()=>{this.startManualAuth()})}),this.plugin.settings.userInfo&&new d.Setting(e).setName("\u6E05\u9664\u6388\u6743").setDesc("\u6E05\u9664\u5F53\u524D\u7684\u6388\u6743\u4FE1\u606F").addButton(g=>{g.setButtonText("\u{1F5D1}\uFE0F \u6E05\u9664\u6388\u6743").setWarning().onClick(async()=>{this.plugin.settings.accessToken="",this.plugin.settings.refreshToken="",this.plugin.settings.userInfo=null,await this.plugin.saveSettings(),this.plugin.feishuApi.updateSettings(this.plugin.settings),new d.Notice("\u2705 \u6388\u6743\u4FE1\u606F\u5DF2\u6E05\u9664"),this.display()})}),e.createEl("h3",{text:"\u{1F4DD} \u5185\u5BB9\u5904\u7406\u8BBE\u7F6E"}),new d.Setting(e).setName("\u6587\u6863\u6807\u9898\u6765\u6E90").setDesc("\u9009\u62E9\u751F\u6210\u7684\u98DE\u4E66\u6587\u6863\u6807\u9898\u4F7F\u7528\u54EA\u4E2A\u6765\u6E90").addDropdown(g=>{g.addOption("filename","\u6587\u4EF6\u540D (Filename)").addOption("frontmatter",'YAML Front Matter \u7684 "title" \u5C5E\u6027').setValue(this.plugin.settings.titleSource).onChange(async v=>{this.plugin.settings.titleSource=v,await this.plugin.saveSettings()})}),new d.Setting(e).setName("Front Matter \u5904\u7406").setDesc("\u9009\u62E9\u5982\u4F55\u5904\u7406\u7B14\u8BB0\u9876\u90E8\u7684 YAML \u5C5E\u6027\u533A").addDropdown(g=>{g.addOption("remove","\u79FB\u9664 (Remove)").addOption("keep-as-code","\u4FDD\u7559\u4E3A\u4EE3\u7801\u5757 (Keep as Code Block)").setValue(this.plugin.settings.frontMatterHandling).onChange(async v=>{this.plugin.settings.frontMatterHandling=v,await this.plugin.saveSettings()})}),e.createEl("h3",{text:"\u{1F517} \u5206\u4EAB\u6743\u9650\u8BBE\u7F6E"}),new d.Setting(e).setName("\u542F\u7528\u94FE\u63A5\u5206\u4EAB").setDesc("\u662F\u5426\u4E3A\u5206\u4EAB\u7684\u6587\u6863\u8BBE\u7F6E\u94FE\u63A5\u5206\u4EAB\u6743\u9650\uFF0C\u8BA9\u7EC4\u7EC7\u5185\u7684\u4EBA\u53EF\u4EE5\u901A\u8FC7\u94FE\u63A5\u8BBF\u95EE").addToggle(g=>{g.setValue(this.plugin.settings.enableLinkShare).onChange(async v=>{this.plugin.settings.enableLinkShare=v,await this.plugin.saveSettings(),this.display()})}),this.plugin.settings.enableLinkShare&&new d.Setting(e).setName("\u94FE\u63A5\u5206\u4EAB\u6743\u9650").setDesc("\u8BBE\u7F6E\u83B7\u5F97\u94FE\u63A5\u7684\u4EBA\u7684\u8BBF\u95EE\u6743\u9650\u3002\u6CE8\u610F\uFF1A\u4E92\u8054\u7F51\u8BBF\u95EE\u9700\u8981\u4F01\u4E1A\u7BA1\u7406\u5458\u5141\u8BB8\u5916\u90E8\u5206\u4EAB").addDropdown(g=>{g.addOption("anyone_readable","\u{1F310} \u4E92\u8054\u7F51\u4E0A\u83B7\u5F97\u94FE\u63A5\u7684\u4EFB\u4F55\u4EBA\u53EF\u9605\u8BFB").addOption("anyone_editable","\u{1F310} \u4E92\u8054\u7F51\u4E0A\u83B7\u5F97\u94FE\u63A5\u7684\u4EFB\u4F55\u4EBA\u53EF\u7F16\u8F91").addOption("tenant_readable","\u{1F3E2} \u7EC4\u7EC7\u5185\u83B7\u5F97\u94FE\u63A5\u7684\u4EBA\u53EF\u9605\u8BFB").addOption("tenant_editable","\u{1F3E2} \u7EC4\u7EC7\u5185\u83B7\u5F97\u94FE\u63A5\u7684\u4EBA\u53EF\u7F16\u8F91").setValue(this.plugin.settings.linkSharePermission).onChange(async v=>{this.plugin.settings.linkSharePermission=v,await this.plugin.saveSettings()})}),this.plugin.settings.userInfo&&(e.createEl("h3",{text:"\u{1F4C1} \u9ED8\u8BA4\u6587\u4EF6\u5939"}),new d.Setting(e).setName("\u5F53\u524D\u9ED8\u8BA4\u6587\u4EF6\u5939").setDesc(`\u6587\u6863\u5C06\u4FDD\u5B58\u5230\uFF1A${this.plugin.settings.defaultFolderName||"\u6211\u7684\u7A7A\u95F4"}${this.plugin.settings.defaultFolderId?` (ID: ${this.plugin.settings.defaultFolderId})`:""}`).addButton(g=>{g.setButtonText("\u{1F4C1} \u9009\u62E9\u6587\u4EF6\u5939").onClick(()=>{this.showFolderSelectModal()})})),e.createEl("h3",{text:"\u{1F4D6} \u4F7F\u7528\u8BF4\u660E"});let i=e.createDiv("setting-item-description"),a=i.createDiv("feishu-usage-link");a.createEl("strong",{text:"\u{1F4DA} \u8BE6\u7EC6\u4F7F\u7528\u8BF4\u660E"}),a.createEl("br");let l=a.createEl("a",{text:"\u{1F517} \u70B9\u51FB\u67E5\u770B\u5B8C\u6574\u4F7F\u7528\u6559\u7A0B",href:"https://l0c34idk7v.feishu.cn/docx/Zk2VdWJPfoqmZhxPSJmcMfSbnHe"});l.target="_blank";let h=i.createDiv("feishu-usage-guide");h.createEl("strong",{text:"\u{1F4CB} \u5FEB\u901F\u914D\u7F6E\u6307\u5357",cls:"feishu-usage-guide-title"});let c=h.createEl("ol"),k=c.createEl("li");k.createEl("strong",{text:"\u521B\u5EFA\u98DE\u4E66\u5E94\u7528\uFF1A"}),k.appendText("\u8BBF\u95EE ");let b=k.createEl("a",{text:"\u98DE\u4E66\u5F00\u653E\u5E73\u53F0 \u{1F517}",href:"https://open.feishu.cn/app"});b.target="_blank",k.appendText(' \u521B\u5EFA"\u4F01\u4E1A\u81EA\u5EFA\u5E94\u7528"\uFF0C\u83B7\u53D6App ID\u548CApp Secret');let x=c.createEl("li");x.createEl("strong",{text:"\u914D\u7F6EOAuth\u56DE\u8C03\uFF1A"}),x.appendText('\u5728\u98DE\u4E66\u5E94\u7528"\u5B89\u5168\u8BBE\u7F6E"\u4E2D\u6DFB\u52A0\u56DE\u8C03\u5730\u5740\uFF1A'),x.createEl("br"),x.createEl("code",{text:"https://md2feishu.xinqi.life/oauth-callback"}),x.createEl("br"),x.createEl("span",{text:"\u{1F4A1} \u9ED8\u8BA4\u4F7F\u7528\u6211\u4EEC\u7684\u56DE\u8C03\u670D\u52A1\uFF0C\u4EE3\u7801\u5F00\u6E90\u53EF\u81EA\u884C\u90E8\u7F72",cls:"hint"});let y=c.createEl("li");y.createEl("strong",{text:"\u6DFB\u52A0\u5E94\u7528\u6743\u9650\uFF1A"}),y.appendText('\u5728"\u6743\u9650\u7BA1\u7406"\u4E2D\u6DFB\u52A0\u4EE5\u4E0B\u6743\u9650\uFF1A');let m=y.createEl("ul");m.createEl("li",{text:"contact:user.base:readonly - \u83B7\u53D6\u7528\u6237\u57FA\u672C\u4FE1\u606F"}),m.createEl("li",{text:"docx:document - \u521B\u5EFA\u3001\u7F16\u8F91\u6587\u6863"}),m.createEl("li",{text:"drive:drive - \u8BBF\u95EE\u4E91\u7A7A\u95F4\u6587\u4EF6"});let F=c.createEl("li");F.createEl("strong",{text:"\u5B8C\u6210\u6388\u6743\uFF1A"}),F.appendText('\u5728\u4E0A\u65B9\u8F93\u5165App ID\u548CApp Secret\uFF0C\u70B9\u51FB"\u{1F680} \u4E00\u952E\u6388\u6743"');let T=c.createEl("li");T.createEl("strong",{text:"\u9009\u62E9\u6587\u4EF6\u5939\uFF1A"}),T.appendText("\u6388\u6743\u540E\u53EF\u9009\u62E9\u9ED8\u8BA4\u4FDD\u5B58\u6587\u4EF6\u5939\uFF08\u53EF\u9009\uFF09");let $=c.createEl("li");$.createEl("strong",{text:"\u5F00\u59CB\u4F7F\u7528\uFF1A"}),$.appendText('\u53F3\u952EMD\u6587\u4EF6\u9009\u62E9"\u{1F4E4} \u5206\u4EAB\u5230\u98DE\u4E66"\uFF0C\u6216\u4F7F\u7528\u547D\u4EE4\u9762\u677F');let U=i.createDiv("feishu-usage-guide");U.createEl("strong",{text:"\u{1F389} \u529F\u80FD\u7279\u8272\uFF1A",cls:"feishu-usage-guide-title"});let E=U.createEl("ul");E.createEl("li",{text:"\u2705 \u667A\u80FD\u6388\u6743\uFF1A\u81EA\u52A8\u68C0\u6D4Btoken\u72B6\u6001\uFF0C\u5931\u6548\u65F6\u81EA\u52A8\u91CD\u65B0\u6388\u6743"}),E.createEl("li",{text:"\u2705 \u65E0\u7F1D\u5206\u4EAB\uFF1A\u4E00\u952E\u5206\u4EAB\uFF0C\u81EA\u52A8\u5904\u7406\u6388\u6743\u548C\u8F6C\u6362\u6D41\u7A0B"}),E.createEl("li",{text:"\u2705 \u683C\u5F0F\u4FDD\u6301\uFF1A\u5B8C\u7F8E\u4FDD\u6301Markdown\u683C\u5F0F\uFF0C\u5305\u62EC\u56FE\u7247\u3001\u8868\u683C\u3001\u4EE3\u7801\u5757"}),E.createEl("li",{text:"\u2705 \u667A\u80FD\u5904\u7406\uFF1A\u81EA\u52A8\u5904\u7406Obsidian\u53CC\u5411\u94FE\u63A5\u3001\u6807\u7B7E\u7B49\u8BED\u6CD5"}),E.createEl("li",{text:"\u2705 \u53EF\u89C6\u5316\u9009\u62E9\uFF1A\u652F\u6301\u6D4F\u89C8\u548C\u9009\u62E9\u76EE\u6807\u6587\u4EF6\u5939"}),E.createEl("li",{text:"\u2705 \u94FE\u63A5\u5206\u4EAB\uFF1A\u81EA\u52A8\u8BBE\u7F6E\u6587\u6863\u5206\u4EAB\u6743\u9650\uFF0C\u652F\u6301\u7EC4\u7EC7\u5185\u94FE\u63A5\u8BBF\u95EE"}),E.createEl("li",{text:"\u2705 \u4E00\u952E\u590D\u5236\uFF1A\u5206\u4EAB\u6210\u529F\u540E\u53EF\u4E00\u952E\u590D\u5236\u6587\u6863\u94FE\u63A5"}),this.addAuthorSection(e)}addAuthorSection(e){e.createEl("hr",{cls:"feishu-author-separator"});let t=e.createDiv({cls:"feishu-author-section"});t.createEl("h4",{text:"\u{1F468}\u200D\u{1F4BB} \u4E86\u89E3\u4F5C\u8005",cls:"feishu-author-title"}),t.createEl("p",{text:"\u60F3\u4E86\u89E3\u66F4\u591A\u5173\u4E8E\u4F5C\u8005\u548C\u5176\u4ED6\u9879\u76EE\u7684\u4FE1\u606F\uFF1F",cls:"feishu-author-description"}),t.createEl("button",{text:"\u{1F310} \u8BBF\u95EE\u4F5C\u8005\u4E3B\u9875",cls:"feishu-author-button"}).addEventListener("click",()=>{window.open("https://ai.xinqi.life/about","_blank")})}startAutoAuth(){if(!this.plugin.settings.appId||!this.plugin.settings.appSecret){new d.Notice("\u274C \u8BF7\u5148\u914D\u7F6E App ID \u548C App Secret"),console.error("Missing App ID or App Secret");return}this.plugin.feishuApi.updateSettings(this.plugin.settings);try{let e=this.plugin.feishuApi.generateAuthUrl();window.open(e,"_blank"),new d.Notice("\u{1F504} \u5DF2\u6253\u5F00\u6D4F\u89C8\u5668\u8FDB\u884C\u6388\u6743\uFF0C\u5B8C\u6210\u540E\u5C06\u81EA\u52A8\u8FD4\u56DEObsidian");let t=()=>{this.display(),window.removeEventListener("feishu-auth-success",t)};window.addEventListener("feishu-auth-success",t)}catch(e){console.error("Auto auth error:",e),new d.Notice(`\u274C \u81EA\u52A8\u6388\u6743\u5931\u8D25: ${e.message}`)}}startManualAuth(){if(!this.plugin.settings.appId||!this.plugin.settings.appSecret){new d.Notice("\u274C \u8BF7\u5148\u914D\u7F6E App ID \u548C App Secret");return}try{this.plugin.feishuApi.updateSettings(this.plugin.settings),new _(this.app,this.plugin.feishuApi,async()=>{await this.plugin.saveSettings(),this.display()}).open()}catch(e){console.error("[Feishu Plugin] Failed to start manual auth:",e),new d.Notice("\u274C \u542F\u52A8\u6388\u6743\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5")}}showFolderSelectModal(){try{new C(this.app,this.plugin.feishuApi,async t=>{try{t?(this.plugin.settings.defaultFolderId=t.folder_token||t.token||"",this.plugin.settings.defaultFolderName=t.name):(console.log("[Feishu Plugin] Root folder selected (\u6211\u7684\u7A7A\u95F4)"),this.plugin.settings.defaultFolderId="",this.plugin.settings.defaultFolderName="\u6211\u7684\u7A7A\u95F4"),await this.plugin.saveSettings(),new d.Notice("\u2705 \u9ED8\u8BA4\u6587\u4EF6\u5939\u8BBE\u7F6E\u5DF2\u4FDD\u5B58"),this.display()}catch(r){console.error("[Feishu Plugin] Failed to save folder settings:",r),new d.Notice("\u274C \u4FDD\u5B58\u6587\u4EF6\u5939\u8BBE\u7F6E\u5931\u8D25")}}).open()}catch(e){console.error("[Feishu Plugin] Failed to open folder selection modal:",e),new d.Notice("\u274C \u6253\u5F00\u6587\u4EF6\u5939\u9009\u62E9\u5931\u8D25")}}};var I=require("obsidian");var L=class{constructor(s){this.localFiles=[];this.app=s}process(s){let e=s;return e=this.processWikiLinks(e),e=this.processBlockReferences(e),e=this.processTags(e),e=this.processEmbeds(e),e=this.processImages(e),e=this.cleanupWhitespace(e),e}processWikiLinks(s,e){return s.replace(/\[\[([^\]|]+)(\|([^\]]+))?\]\]/g,(t,r,o,n)=>{if(this.isFileReference(r)){let i=this.generatePlaceholder(),a={originalPath:r,fileName:this.extractFileName(r),placeholder:i,isImage:this.isImageFile(r),altText:n||r};return this.localFiles.push(a),i}else{let i=this.findLinkedMarkdownFile(r);if(i&&e){let a=(0,I.normalizePath)(i.path);if(e.processedFiles.has(a))return console.warn(`\u26A0\uFE0F Circular reference detected for file: ${a}`),`\u{1F4DD} ${n||r} (\u5FAA\u73AF\u5F15\u7528)`;if(e.currentDepth>=e.maxDepth)return console.warn(`\u26A0\uFE0F Max depth reached for file: ${a}`),`\u{1F4DD} ${n||r} (\u6DF1\u5EA6\u9650\u5236)`;let l=this.generatePlaceholder(),h={originalPath:i.path,fileName:i.basename,placeholder:l,isImage:!1,isSubDocument:!0,altText:n||r};return this.localFiles.push(h),l}else return`\u{1F4DD} ${n||r}`}})}processBlockReferences(s){return s.replace(/\[\[([^#\]]+)#\^([^\]]+)\]\]/g,(e,t,r)=>`\u{1F4DD} ${t} (\u5757\u5F15\u7528: ${r})`)}processTags(s){return s.replace(/#([a-zA-Z0-9_\u4e00-\u9fff]+)/g,(e,t)=>`#${t}`)}processEmbeds(s){return s.replace(/!\[\[([^\]]+)\]\]/g,(e,t)=>{let r=this.generatePlaceholder(),o={originalPath:t,fileName:this.extractFileName(t),placeholder:r,isImage:this.isImageFile(t),altText:t};return this.localFiles.push(o),r})}processImages(s){return s.replace(/!\[([^\]]*)\]\(([^)]+)\)/g,(e,t,r)=>{if(r.startsWith("http://")||r.startsWith("https://"))return e;let o=this.generatePlaceholder(),n=t||"\u56FE\u7247",i={originalPath:r,fileName:this.extractFileName(r),placeholder:o,isImage:!0,altText:n};return this.localFiles.push(i),o})}cleanupWhitespace(s){return s=s.replace(/\n{3,}/g,`

`),s=s.replace(/[ \t]+$/gm,""),s=s.replace(/\s+$/,`
`),s}processCodeBlocks(s){return s.replace(/```(\w+)[\s\S]*?```/g,(e,t)=>e)}processMathFormulas(s){return s=s.replace(/\$([^$]+)\$/g,(e,t)=>`\u{1F4D0} \u6570\u5B66\u516C\u5F0F: ${t}`),s=s.replace(/\$\$([^$]+)\$\$/g,(e,t)=>`
\u{1F4D0} \u6570\u5B66\u516C\u5F0F\u5757:
${t}
`),s}processHighlights(s){return s.replace(/==([^=]+)==/g,(e,t)=>`<mark>${t}</mark>`)}processCallouts(s){let e=/^>\s*\[!([^\]]+)\](-?)\s*([^\n]*)\n((?:(?:>[^\n]*|)\n?)*?)(?=\n(?!>)|$)/gm;return s.replace(e,(t,r,o,n,i)=>{let a=r.toLowerCase().trim(),l=M[a]||M.default,h=n.trim()||l.title;h=this.escapeMarkdownInTitle(h);let b=i.split(`
`).map(m=>m.startsWith(">")?m.replace(/^>\s?/,""):m).filter((m,F,T)=>!(m===""&&F===T.length-1)).join(`
`),x=`**${l.emoji} ${h}**`,y=b.split(`
`).map(m=>m.trim()===""?">":`> ${m}`).join(`
`);return`
> ${x}
>
${y}

`})}escapeMarkdownInTitle(s){return s.replace(/\*\*/g,"*")}processComplete(s){let e=s;return e=this.processWikiLinks(e),e=this.processBlockReferences(e),e=this.processEmbeds(e),e=this.processImages(e),e=this.processTags(e),e=this.processHighlights(e),e=this.processMathFormulas(e),e=this.processCodeBlocks(e),e=this.cleanupWhitespace(e),e}processCompleteWithFiles(s,e=3,t="remove"){this.localFiles=[];let{content:r,frontMatter:o}=this.processFrontMatter(s,t),n={maxDepth:e,currentDepth:0,processedFiles:new Set};return{content:this.processCompleteWithContext(r,n),localFiles:[...this.localFiles],frontMatter:o,extractedTitle:(o==null?void 0:o.title)||null}}generatePlaceholder(){let s=Date.now(),e=Math.random().toString(36).substring(2,8);return`__FEISHU_FILE_${s}_${e}__`}extractFileName(s){return s.split(/[/\\]/).pop()||s}isFileReference(s){let e=this.extractFileName(s);return e.includes(".")&&e.lastIndexOf(".")>0}isImageFile(s){let e=[".jpg",".jpeg",".png",".gif",".bmp",".svg",".webp"],t=s.toLowerCase().substring(s.lastIndexOf("."));return e.includes(t)}getLocalFiles(){return[...this.localFiles]}clearLocalFiles(){this.localFiles=[]}findLinkedMarkdownFile(s){var e;try{let t=s.trim();t=t.replace(/^\.\//,"").replace(/^\//,""),t.includes(".")||(t=t+".md");let r=(0,I.normalizePath)(t),o=this.app.vault.getFileByPath(r);if(!o){let n=(e=r.split("/").pop())==null?void 0:e.toLowerCase();n&&(o=this.app.vault.getMarkdownFiles().find(a=>a.name.toLowerCase()===n)||null)}if(!o){let n=s.trim().toLowerCase();o=this.app.vault.getMarkdownFiles().find(a=>a.basename.toLowerCase()===n)||null}return console.log(o?`\u2705 Found linked markdown file: "${s}" -> "${o.path}"`:`\u274C Linked markdown file not found: "${s}"`),o}catch(t){return console.error(`Error finding linked file for "${s}":`,t),null}}async processSubDocument(s,e){try{let t=(0,I.normalizePath)(s.path);e.processedFiles.add(t);let r=await this.app.vault.read(s),o={...e,currentDepth:e.currentDepth+1},n=[...this.localFiles];this.localFiles=[];let i=this.processCompleteWithContext(r,o),a=[...this.localFiles];return this.localFiles=n,{content:i,localFiles:a,frontMatter:null,extractedTitle:null}}catch(t){return console.error(`Error processing sub-document ${s.path}:`,t),{content:`\u274C \u65E0\u6CD5\u8BFB\u53D6\u5B50\u6587\u6863: ${s.basename}`,localFiles:[],frontMatter:null,extractedTitle:null}}}processCompleteWithContext(s,e){let t=s;return t=this.processCallouts(t),t=this.processWikiLinks(t,e),t=this.processBlockReferences(t),t=this.processEmbeds(t),t=this.processImages(t),t=this.processTags(t),t=this.processHighlights(t),t=this.processMathFormulas(t),t=this.processCodeBlocks(t),t=this.cleanupWhitespace(t),t}parseFrontMatter(s){if(!s.startsWith(`---
`)&&!s.startsWith(`---\r
`))return{frontMatter:null,content:s};let e=s.split(`
`),t=-1;for(let n=1;n<e.length;n++)if(e[n].trim()==="---"){t=n;break}if(t===-1)return{frontMatter:null,content:s};let r=e.slice(1,t).join(`
`),o=e.slice(t+1).join(`
`);try{return{frontMatter:this.parseSimpleYaml(r),content:o}}catch(n){return console.warn("Failed to parse Front Matter:",n),{frontMatter:null,content:s}}}parseSimpleYaml(s){let e={},t=s.split(`
`);for(let r of t){let o=r.trim();if(!o||o.startsWith("#"))continue;let n=o.indexOf(":");if(n===-1)continue;let i=o.substring(0,n).trim(),a=o.substring(n+1).trim();(a.startsWith('"')&&a.endsWith('"')||a.startsWith("'")&&a.endsWith("'"))&&(a=a.slice(1,-1)),e[i]=a}return e}processFrontMatter(s,e){let{frontMatter:t,content:r}=this.parseFrontMatter(s);if(!t)return{content:s,frontMatter:null};if(e==="remove")return{content:r,frontMatter:t};{let o=s.split(`
`),n=-1;for(let i=1;i<o.length;i++)if(o[i].trim()==="---"){n=i;break}if(n!==-1)return{content:"```yaml\n"+o.slice(1,n).join(`
`)+"\n```\n\n"+r,frontMatter:t}}return{content:r,frontMatter:t}}extractTitle(s,e,t){return t==="frontmatter"&&(e!=null&&e.title)?e.title:s}};var R=class extends f.Plugin{async onload(){await this.loadSettings(),this.feishuApi=new P(this.settings,this.app),this.markdownProcessor=new L(this.app),this.registerObsidianProtocolHandler("feishu-auth",e=>{this.handleOAuthCallback(e)}),this.addSettingTab(new A(this.app,this)),this.registerCommands(),this.registerMenus()}onunload(){}registerCommands(){this.addCommand({id:"share-current-note",name:"\u5206\u4EAB\u5F53\u524D\u7B14\u8BB0\u5230\u98DE\u4E66",editorCallback:(e,t)=>{this.shareCurrentNote()}})}registerMenus(){this.registerEvent(this.app.workspace.on("file-menu",(e,t)=>{t instanceof f.TFile&&t.extension==="md"&&e.addItem(r=>{r.setTitle("\u{1F4E4} \u5206\u4EAB\u5230\u98DE\u4E66").setIcon("share").onClick(()=>{this.shareFile(t)})})})),this.registerEvent(this.app.workspace.on("editor-menu",(e,t,r)=>{e.addItem(o=>{o.setTitle("\u{1F4E4} \u5206\u4EAB\u5230\u98DE\u4E66").setIcon("share").onClick(()=>{this.shareCurrentNote()})})}))}async loadSettings(){let e=await this.loadData();this.settings=Object.assign({},O,e)}async saveSettings(){await this.saveData(this.settings),this.feishuApi&&this.feishuApi.updateSettings(this.settings)}async handleOAuthCallback(e){if(this.log("Processing OAuth callback"),e.code){new f.Notice("\u{1F504} \u6B63\u5728\u5904\u7406\u6388\u6743\u56DE\u8C03...");try{await this.feishuApi.processCallback(`obsidian://feishu-auth?${new URLSearchParams(e).toString()}`)?(this.log("OAuth authorization successful"),new f.Notice("\u{1F389} \u81EA\u52A8\u6388\u6743\u6210\u529F\uFF01"),await this.saveSettings(),window.dispatchEvent(new CustomEvent("feishu-auth-success",{detail:{timestamp:Date.now(),source:"oauth-callback"}}))):(this.log("OAuth authorization failed","warn"),new f.Notice("\u274C \u6388\u6743\u5904\u7406\u5931\u8D25\uFF0C\u8BF7\u91CD\u8BD5"))}catch(t){this.handleError(t,"OAuth\u56DE\u8C03\u5904\u7406")}}else if(e.error){let t=e.error_description||e.error;this.log(`OAuth error: ${t}`,"error"),new f.Notice(`\u274C \u6388\u6743\u5931\u8D25: ${t}`)}else this.log("Invalid OAuth callback parameters","warn"),new f.Notice("\u274C \u65E0\u6548\u7684\u6388\u6743\u56DE\u8C03")}async shareCurrentNote(){this.log("Attempting to share current note");let e=this.app.workspace.getActiveFile();if(!e){this.log("No active file found","warn"),new f.Notice("\u274C \u6CA1\u6709\u6253\u5F00\u7684\u7B14\u8BB0");return}if(e.extension!=="md"){this.log(`Unsupported file type: ${e.extension}`,"warn"),new f.Notice("\u274C \u53EA\u652F\u6301\u5206\u4EAB Markdown \u6587\u4EF6");return}this.log(`Sharing file: ${e.path}`),await this.shareFile(e)}async shareFile(e){this.log(`Starting file share process for: ${e.path}`);let t=new f.Notice("\u{1F504} \u6B63\u5728\u5206\u4EAB\u5230\u98DE\u4E66...",0);try{if(!this.settings.accessToken||!this.settings.userInfo){this.log("Authorization required","warn"),t.hide(),new f.Notice("\u274C \u8BF7\u5148\u5728\u8BBE\u7F6E\u4E2D\u5B8C\u6210\u98DE\u4E66\u6388\u6743");return}this.log("Reading file content");let r=await this.app.vault.read(e),o=this.markdownProcessor.processCompleteWithFiles(r,3,this.settings.frontMatterHandling),n=this.markdownProcessor.extractTitle(e.basename,o.frontMatter,this.settings.titleSource);this.log(`Processing file with title: ${n}`);let i=await this.feishuApi.shareMarkdownWithFiles(n,o,t);t.hide(),i.success?(this.log(`File shared successfully: ${i.title}`),this.showSuccessNotification(i)):(this.log(`Share failed: ${i.error}`,"error"),new f.Notice(`\u274C \u5206\u4EAB\u5931\u8D25\uFF1A${i.error}`))}catch(r){t.hide(),this.handleError(r,"\u6587\u4EF6\u5206\u4EAB")}}async ensureValidAuth(){return!!this.settings.accessToken}showSuccessNotification(e){if(e.url){let t=`\u2705 \u5206\u4EAB\u6210\u529F\uFF01\u6587\u6863\uFF1A${e.title}`,r=new f.Notice(t,8e3),o=r.noticeEl.createEl("button",{text:"\u{1F4CB} \u590D\u5236\u94FE\u63A5",cls:"mod-cta"});o.onclick=async()=>{try{await navigator.clipboard.writeText(e.url),this.log("URL copied to clipboard"),o.textContent="\u2705 \u5DF2\u590D\u5236",setTimeout(()=>{o.textContent="\u{1F4CB} \u590D\u5236\u94FE\u63A5"},2e3)}catch(i){this.log(`Failed to copy URL: ${i.message}`,"error"),new f.Notice("\u274C \u590D\u5236\u5931\u8D25")}};let n=r.noticeEl.createEl("button",{text:"\u{1F517} \u6253\u5F00",cls:"mod-muted"});n.onclick=()=>{e.url&&window.open(e.url,"_blank")}}else new f.Notice(`\u2705 \u5206\u4EAB\u6210\u529F\uFF01\u6587\u6863\u6807\u9898\uFF1A${e.title}`)}handleError(e,t,r){console.error(`[Feishu Plugin] ${t}:`,e);let o=r||`\u274C ${t}\u5931\u8D25: ${e.message}`;new f.Notice(o)}log(e,t="info"){let r="[Feishu Plugin]";switch(t){case"error":console.error(`${r} ${e}`);break;case"warn":console.warn(`${r} ${e}`);break;default:console.log(`${r} ${e}`)}}};
